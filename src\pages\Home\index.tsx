import GaoDeMap from '@/components/GaoDeMap';
import PublicLayout from '@/components/PublicLayout';
import { mapData } from '@/services/mockData';
import { history } from '@umijs/max';
import { Button, Image, Modal, Typography } from 'antd';
import React, { useState } from 'react';

const { Title, Paragraph } = Typography;

const HomePage: React.FC = () => {
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [modalVisible, setModalVisible] = useState(false);

  // 准备地图标记点数据
  const prepareMapMarkers = () => {
    const markers: any[] = [];

    // 添加山塬标记点
    mapData.mountains.forEach((mountain) => {
      markers.push({
        position: [mountain.longitude, mountain.latitude],
        title: mountain.name,
        content: `<div style="padding: 10px;">
          <h4>${mountain.name}</h4>
          <p>海拔：${mountain.height}米</p>
          <p>${mountain.historical_records}</p>
        </div>`,
        id: `mountain_${mountain.id}`,
        status: 1, // 山塬状态
        icon: {
          image: AMAP_CONFIG.markerIcons.mountain,
          size: [32, 32],
          offset: [16, 32],
        },
        data: { ...mountain, type: 'mountain' },
      });
    });

    // 添加水系标记点
    mapData.waterSystems.forEach((waterSystem) => {
      markers.push({
        position: [waterSystem.longitude, waterSystem.latitude],
        title: waterSystem.name,
        content: `<div style="padding: 10px;">
          <h4>${waterSystem.name}</h4>
          <p>长度：${waterSystem.length_area}</p>
          <p>${waterSystem.historical_records}</p>
        </div>`,
        id: `water_${waterSystem.id}`,
        status: 2, // 水系状态
        icon: {
          image: AMAP_CONFIG.markerIcons.water,
          size: [32, 32],
          offset: [16, 32],
        },
        data: { ...waterSystem, type: 'waterSystem' },
      });
    });

    // 添加历史要素标记点
    mapData.historicalElements.forEach((element) => {
      markers.push({
        position: [
          element.construction_longitude,
          element.construction_latitude,
        ],
        title: element.name,
        content: `<div style="padding: 10px;">
          <h4>${element.name}</h4>
          <p>建造时间：${new Date(
            element.construction_time,
          ).getFullYear()}年</p>
          <p>${element.historical_records}</p>
        </div>`,
        id: `historical_${element.id}`,
        status: 3, // 历史要素状态
        icon: {
          image: AMAP_CONFIG.markerIcons.historical,
          size: [32, 32],
          offset: [16, 32],
        },
        data: { ...element, type: 'historicalElement' },
      });
    });

    return markers;
  };

  // 地图标记点点击事件处理
  const handleMarkerClick = (marker: any, e: any) => {
    console.log('标记点点击:', marker, e);
    if (marker.data) {
      setSelectedItem(marker.data);
      setModalVisible(true);
    }
  };

  // 地图点击事件处理
  const handleMapClick = (e: any) => {
    console.log('地图点击:', e);
  };

  // 地图创建完成回调
  const handleMapCreated = (mapInstance: any) => {
    console.log('地图创建完成:', mapInstance);
  };

  const handleDetailClick = () => {
    if (selectedItem) {
      history.push(`/detail/${selectedItem.type}/${selectedItem.id}`);
    }
    setModalVisible(false);
  };

  return (
    <PublicLayout>
      <div className="content-card">
        <div style={{ padding: '24px 24px 0' }}>
          <Title level={2} style={{ textAlign: 'center', marginBottom: 16 }}>
            关中地区智慧营建系统
          </Title>
          <Paragraph
            style={{ textAlign: 'center', fontSize: 16, marginBottom: 24 }}
          >
            探索关中地区的山塬、水系与历史要素，感受千年文明的智慧传承
          </Paragraph>
        </div>

        <div className="map-container">
          <GaoDeMap
            city="西安"
            center={[108.9398, 34.3412]} // 关中地区中心坐标
            zoom={8}
            activedZoom={12}
            markers={prepareMapMarkers()}
            enableInfoWindow={true}
            enableCluster={false}
            events={{
              onClick: handleMapClick,
              onMarkerClick: handleMarkerClick,
            }}
            onMapCreated={handleMapCreated}
            style={{ width: '100%', height: '100%' }}
          />
        </div>

        <div style={{ padding: '24px', textAlign: 'center' }}>
          <Paragraph>
            点击地图上的标记点查看详细信息，或通过导航栏浏览不同类型的要素
          </Paragraph>
        </div>
      </div>

      <Modal
        title={selectedItem?.name}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setModalVisible(false)}>
            关闭
          </Button>,
          <Button key="detail" type="primary" onClick={handleDetailClick}>
            查看详情
          </Button>,
        ]}
        width={600}
      >
        {selectedItem && (
          <div>
            <Paragraph>{selectedItem.historical_records}</Paragraph>
            {selectedItem.photos && selectedItem.photos.length > 0 && (
              <div style={{ marginTop: 16 }}>
                <Image.PreviewGroup>
                  {selectedItem.photos.slice(0, 2).map((photo: any) => (
                    <Image
                      key={photo.id}
                      width={200}
                      src={photo.url}
                      alt={photo.name}
                      style={{ marginRight: 8 }}
                    />
                  ))}
                </Image.PreviewGroup>
              </div>
            )}
          </div>
        )}
      </Modal>
    </PublicLayout>
  );
};

export default HomePage;
