import GaoDeMap from '@/components/GaoDeMap';
import PublicLayout from '@/components/PublicLayout';
import { mapData } from '@/services/mockData';
import { Typography } from 'antd';
import React from 'react';

const { Title, Paragraph } = Typography;

const HomePage: React.FC = () => {
  // 生成信息窗口内容，包含查看详情按钮
  const generateInfoWindowContent = (item: any, type: string) => {
    const detailUrl = `/detail/${type}/${item.id}`;
    let basicInfo = '';

    if (type === 'mountain') {
      basicInfo = `<p>海拔：${item.height}米</p>`;
    } else if (type === 'waterSystem') {
      basicInfo = `<p>长度：${item.length_area}</p>`;
    } else if (type === 'historicalElement') {
      basicInfo = `<p>建造时间：${new Date(item.construction_time).getFullYear()}年</p>`;
    }

    return `<div style="padding: 10px; min-width: 200px;">
      <h4 style="margin: 0 0 8px 0; color: #333;">${item.name}</h4>
      ${basicInfo}
      <p style="margin: 8px 0; color: #666; font-size: 13px;">${item.historical_records}</p>
      <div style="margin-top: 12px; text-align: center;">
        <a href="${detailUrl}" style="display: inline-block; padding: 6px 16px; background: #1890ff; color: white; text-decoration: none; border-radius: 4px; font-size: 12px;">查看详情</a>
      </div>
    </div>`;
  };

  // 准备地图标记点数据
  const prepareMapMarkers = () => {
    const markers: any[] = [];

    // 添加山塬标记点
    mapData.mountains.forEach((mountain) => {
      markers.push({
        position: [mountain.longitude, mountain.latitude],
        title: mountain.name,
        content: generateInfoWindowContent(mountain, 'mountain'),
        id: `mountain_${mountain.id}`,
        status: 1, // 山塬状态
        icon: {
          image: AMAP_CONFIG.markerIcons.mountain,
          size: [32, 32],
          offset: [16, 32],
        },
        data: { ...mountain, type: 'mountain' },
      });
    });

    // 添加水系标记点
    mapData.waterSystems.forEach((waterSystem) => {
      markers.push({
        position: [waterSystem.longitude, waterSystem.latitude],
        title: waterSystem.name,
        content: generateInfoWindowContent(waterSystem, 'waterSystem'),
        id: `water_${waterSystem.id}`,
        status: 2, // 水系状态
        icon: {
          image: AMAP_CONFIG.markerIcons.water,
          size: [32, 32],
          offset: [16, 32],
        },
        data: { ...waterSystem, type: 'waterSystem' },
      });
    });

    // 添加历史要素标记点
    mapData.historicalElements.forEach((element) => {
      markers.push({
        position: [
          element.construction_longitude,
          element.construction_latitude,
        ],
        title: element.name,
        content: generateInfoWindowContent(element, 'historicalElement'),
        id: `historical_${element.id}`,
        status: 3, // 历史要素状态
        icon: {
          image: AMAP_CONFIG.markerIcons.historical,
          size: [32, 32],
          offset: [16, 32],
        },
        data: { ...element, type: 'historicalElement' },
      });
    });

    return markers;
  };

  // 地图标记点点击事件处理
  const handleMarkerClick = (marker: any, e: any) => {
    console.log('标记点点击:', marker, e);
    // 不再显示自定义Modal，统一使用地图信息窗口
  };

  // 地图点击事件处理
  const handleMapClick = (e: any) => {
    console.log('地图点击:', e);
  };

  // 地图创建完成回调
  const handleMapCreated = (mapInstance: any) => {
    console.log('地图创建完成:', mapInstance);
  };



  return (
    <PublicLayout>
      <div className="content-card">
        <div style={{ padding: '24px 24px 0' }}>
          <Title level={2} style={{ textAlign: 'center', marginBottom: 16 }}>
            关中地区智慧营建系统
          </Title>
          <Paragraph
            style={{ textAlign: 'center', fontSize: 16, marginBottom: 24 }}
          >
            探索关中地区的山塬、水系与历史要素，感受千年文明的智慧传承
          </Paragraph>
        </div>

        <div className="map-container">
          <GaoDeMap
            city="西安"
            center={[108.9398, 34.3412]} // 关中地区中心坐标
            zoom={8}
            activedZoom={12}
            markers={prepareMapMarkers()}
            enableInfoWindow={true}
            enableCluster={false}
            events={{
              onClick: handleMapClick,
              onMarkerClick: handleMarkerClick,
            }}
            onMapCreated={handleMapCreated}
            style={{ width: '100%', height: '100%' }}
          />
        </div>

        <div style={{ padding: '24px', textAlign: 'center' }}>
          <Paragraph>
            点击地图上的标记点查看详细信息，或通过导航栏浏览不同类型的要素
          </Paragraph>
        </div>
      </div>


    </PublicLayout>
  );
};

export default HomePage;
